/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        pathname: '**',
      },
    ],
  },
  webpack: (config, { isDev, webpack }) => {
    if (!isDev) {
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^\.\/app\/dev-tools\/(test-.+|debug\/)/, // Exclude test- pages and debug folder
          contextRegExp: /app/, // Apply only to pages directory
        })
      );
    }
    return config;
  },
};

module.exports = nextConfig